import cv2
import numpy as np
import time


class FaceDetectionService:
    def __init__(
        self,
        face_cascade_path="services/face_models/haarcascade_frontalface_default.xml",
        eye_cascade_path="services/face_models/haarcascade_eye.xml",
        grace_period_sec=2,
    ):
        self.face_cascade = cv2.CascadeClassifier(face_cascade_path)
        self.eye_cascade = cv2.CascadeClassifier(eye_cascade_path)

        self.grace_period_sec = grace_period_sec
        self.last_face_issue_time = None
        self.last_eye_issue_time = None

    def detect(self, img):
        current_time = time.time()
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray, scaleFactor=1.1, minNeighbors=5, minSize=(80, 80)
        )

        if len(faces) == 0:
            return self._handle_grace_period(
                "face",
                current_time,
                "No face detected. Please position your face inside the frame.",
            )

        # Reset face issue timer if face is detected
        self.last_face_issue_time = None

        x, y, w, h = faces[0]
        face_center_x = x + w / 2
        image_width = img.shape[1]

        # Define acceptable horizontal bounds with margin
        left_bound = image_width * 0.20
        right_bound = image_width * 0.80
        movement_margin = image_width * 0.10

        adjusted_left = left_bound - movement_margin
        adjusted_right = right_bound + movement_margin

        if face_center_x < adjusted_left or face_center_x > adjusted_right:
            return self._handle_grace_period(
                "face",
                current_time,
                "Face detected but outside the allowed area. Please look directly at the screen.",
            )
        else:
            self.last_face_issue_time = None

        # Now check eyes
        face_roi_gray = gray[y : y + h, x : x + w]
        eyes = self.eye_cascade.detectMultiScale(
            face_roi_gray, scaleFactor=1.1, minNeighbors=5, minSize=(15, 15)
        )

        if len(eyes) < 2:
            return self._handle_grace_period(
                "eye",
                current_time,
                "Face detected but both eyes were not clearly detected. Please adjust your face.",
            )
        else:
            self.last_eye_issue_time = None

        return {
            "status": "ok",
            "message": "Face and eyes detected properly. Please continue.",
        }

    def _handle_grace_period(self, issue_type, current_time, warning_message):
        if issue_type == "face":
            if not self.last_face_issue_time:
                self.last_face_issue_time = current_time
            elif current_time - self.last_face_issue_time >= self.grace_period_sec:
                return {"status": "warning", "message": warning_message}
        elif issue_type == "eye":
            if not self.last_eye_issue_time:
                self.last_eye_issue_time = current_time
            elif current_time - self.last_eye_issue_time >= self.grace_period_sec:
                return {"status": "warning", "message": warning_message}
        return {
            "status": "ok",
            "message": "Face and eyes detected properly. Please continue.",
        }

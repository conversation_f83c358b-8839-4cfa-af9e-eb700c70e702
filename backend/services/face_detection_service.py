import cv2
import numpy as np
import time


class FaceDetectionService:
    def __init__(
        self,
        face_cascade_path="services/face_models/haarcascade_frontalface_default.xml",
        eye_cascade_path="services/face_models/haarcascade_eye.xml",
    ):
        # Load Haar cascade classifiers
        self.face_cascade = cv2.CascadeClassifier(face_cascade_path)
        self.eye_cascade = cv2.CascadeClassifier(eye_cascade_path)

        # Add tracking for movement smoothing and grace periods
        self.last_valid_position = None
        self.last_warning_time = 0
        self.warning_grace_period = 3.0  # 3 seconds grace period as per user preference
        self.position_history = []  # Track recent positions for smoothing
        self.max_history_size = 5  # Keep last 5 positions for smoothing

    def detect(self, img):
        """
        Detect face and eyes in the given image and return warnings if:
        - No face is detected
        - Face is not centered (with improved tolerance)
        - Eyes are not detected or appear to be looking away
        """
        current_time = time.time()
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Improved face detection parameters for better stability
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.05,  # Reduced from 1.1 for better detection
            minNeighbors=3,    # Reduced from 5 for more tolerance
            minSize=(30, 30),  # Minimum face size
            flags=cv2.CASCADE_SCALE_IMAGE
        )

        if len(faces) == 0:
            # Check if we have a recent valid position to provide more tolerance
            if self.last_valid_position and (current_time - self.last_warning_time) < self.warning_grace_period:
                return {
                    "status": "ok",
                    "message": "Face tracking maintained within grace period.",
                }
            return {
                "status": "warning",
                "message": "No face detected. Please position your face inside the frame.",
            }

        # Use the largest detected face for better stability
        largest_face = max(faces, key=lambda face: face[2] * face[3])
        x, y, w, h = largest_face
        face_center_x = x + w / 2
        face_center_y = y + h / 2
        image_width = img.shape[1]
        image_height = img.shape[0]

        # Store current position for smoothing
        current_position = {"x": face_center_x, "y": face_center_y}
        self.position_history.append(current_position)
        if len(self.position_history) > self.max_history_size:
            self.position_history.pop(0)

        # Calculate smoothed position using recent history
        if len(self.position_history) >= 3:
            avg_x = sum(pos["x"] for pos in self.position_history) / len(self.position_history)
            avg_y = sum(pos["y"] for pos in self.position_history) / len(self.position_history)
            smoothed_center_x = avg_x
            smoothed_center_y = avg_y
        else:
            smoothed_center_x = face_center_x
            smoothed_center_y = face_center_y

        # Expanded acceptable bounds for more freedom (20% - 80% instead of 30% - 70%)
        left_bound = image_width * 0.2
        right_bound = image_width * 0.8
        top_bound = image_height * 0.15
        bottom_bound = image_height * 0.85

        # Check if face is outside bounds using smoothed position
        is_outside_bounds = (
            smoothed_center_x < left_bound or
            smoothed_center_x > right_bound or
            smoothed_center_y < top_bound or
            smoothed_center_y > bottom_bound
        )

        if is_outside_bounds:
            # Apply grace period for position warnings
            if (current_time - self.last_warning_time) < self.warning_grace_period:
                # Update last valid position and continue
                self.last_valid_position = current_position
                return {
                    "status": "ok",
                    "message": "Face position within grace period tolerance.",
                }

            self.last_warning_time = current_time
            return {
                "status": "warning",
                "message": "Face detected but outside the allowed area. Please look directly at the screen.",
            }

        # Update last valid position
        self.last_valid_position = current_position

        # Now, check the eyes within the face region with improved tolerance
        face_roi_gray = gray[y : y + h, x : x + w]
        eyes = self.eye_cascade.detectMultiScale(
            face_roi_gray,
            scaleFactor=1.05,  # More sensitive detection
            minNeighbors=3,    # Reduced for more tolerance
            minSize=(10, 10),  # Smaller minimum eye size
            flags=cv2.CASCADE_SCALE_IMAGE
        )

        # More lenient eye detection - allow for 1 or more eyes instead of requiring exactly 2
        if len(eyes) < 1:
            # Apply grace period for eye detection issues as per user preference
            if (current_time - self.last_warning_time) < self.warning_grace_period:
                return {
                    "status": "ok",
                    "message": "Eye detection within grace period tolerance.",
                }

            self.last_warning_time = current_time
            return {
                "status": "warning",
                "message": "Face detected but both eyes were not clearly detected. Please adjust your face.",
            }

        # Eye gaze detection is commented out as it was causing too many false positives
        # The current implementation focuses on basic eye presence rather than gaze direction
        eye_warnings = []

        # Only trigger eye warnings if there's a persistent issue
        if eye_warnings and (current_time - self.last_warning_time) >= self.warning_grace_period:
            self.last_warning_time = current_time
            return {"status": "warning", "message": " ".join(eye_warnings)}

        # Reset warning timer on successful detection
        if not eye_warnings:
            self.last_warning_time = 0

        return {
            "status": "ok",
            "message": "Face and eyes detected properly. Please continue.",
        }

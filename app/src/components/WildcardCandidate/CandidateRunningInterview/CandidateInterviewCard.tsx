import React from "react";
import { InterviewDetailInterface } from "@src/redux/interfaces";
import { InterviewRatingView } from "@src/components/WildCard";
import Link from "next/link";
import { encrypt } from "@src/helper/encryption";
import { useRouter } from "next/navigation";

type CandidateInterviewCardType = {
  interview: InterviewDetailInterface;
};

export const CandidateInterviewCard: React.FC<CandidateInterviewCardType> = ({
  interview,
}) => {
  const enabledInterview = new Date() > new Date(interview.interview_at);
  const router = useRouter()
  return (
    <li className={[0, 2, 3, 4, 5].includes(interview.status) ? "active" : ""}>
      <div className="box">
        <div className="sub-head">
          <span className="round">
            Round:{" "}
            <b>{interview.interview_round.toString().padStart(2, "0")}</b>
          </span>
          <span className={`badge passed ${interview.status_name}`}>
            {interview.status_name}
          </span>
        </div>
        <div className="head">
          <h4>
            {interview.interview_at?.strftime("%d %b, %Y | %I:%M %p ")}
            <span className={interview.interview_mode_name}>
              ({interview.interview_mode_name})
            </span>
          </h4>
          {interview.interview_mode_name == "Video Call" &&
            interview.meeting_link && (
              <p>
                Meeting Link:{" "}
                <span>
                  <Link
                    href={`${interview.meeting_link ?? ""}`}
                    target="_blank">
                    {interview.meeting_link}
                  </Link>
                </span>
              </p>
            )}
          <p>
            Interviewer: <span>{interview.interviewer_name}</span>
          </p>

          {interview.status == 1
            ? interview.interview_mode_name == "Screening" &&
              enabledInterview && (
                <>
                <button type="button" onClick={()=>router.push(`/candidates/interview/${encrypt(interview.id.toString())}/screening`)} className="ant-btn css-dev-only-do-not-override-n0gjrg ant-btn-default btn btn-primary border-0 mt-5">
                  <span>Join Interview</span>
                </button>
                </>
              )
            : null}
          {interview.interview_mode_name == "Screening" &&
          interview.status != 1 &&
          interview.show_marks &&
          interview.score != undefined ? (
            <p>
              <span>Score: {interview.score}%</span>
            </p>
          ) : null}
        </div>
        {interview.feedback ? (
          <InterviewRatingView
            feedback={interview.feedback}
            showStatus={false}
          />
        ) : null}
      </div>
    </li>
  );
};
